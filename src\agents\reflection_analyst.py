import json
import os
from datetime import datetime, timedelta
from langchain_core.messages import HumanMessage
from langchain_core.prompts import ChatPromptTemplate
from pydantic import BaseModel, Field
from typing_extensions import Literal
from src.graph.state import AgentState, show_agent_reasoning
from src.utils.progress import progress
from src.utils.llm import call_llm


class ReflectionAnalysis(BaseModel):
    decision_quality: Literal["excellent", "good", "fair", "poor"]
    correctness_score: float = Field(description="Score from 0-100 indicating decision correctness")
    key_insights: list[str] = Field(description="Key insights from analyzing the decision")
    recommendations: list[str] = Field(description="Specific recommendations for future decisions")
    reasoning: str = Field(description="Detailed reasoning for the analysis")


class ReflectionOutput(BaseModel):
    reflections: dict[str, ReflectionAnalysis] = Field(description="Dictionary of ticker to reflection analysis")


def reflection_analyst_agent(state: AgentState):
    """
    Reflection Analyst Agent - Analyzes portfolio manager decisions and provides reflection recommendations
    Runs after daily trading ends to provide improvement suggestions for the next day
    """
    data = state["data"]
    tickers = data["tickers"]
    end_date = data["end_date"]

    # Get current portfolio decisions (parse from the last message)
    portfolio_decisions = {}
    for message in reversed(state["messages"]):
        if message.name == "portfolio_manager":
            try:
                portfolio_decisions = json.loads(message.content)
                break
            except json.JSONDecodeError:
                continue

    if not portfolio_decisions:
        progress.update_status("reflection_analyst", None, "No portfolio decisions found")
        return {"messages": state["messages"], "data": data}

    # Get all analyst signals
    analyst_signals = data.get("analyst_signals", {})

    # Generate reflection analysis
    reflection_analysis = {}

    for ticker in tickers:
        if ticker in portfolio_decisions:
            progress.update_status("reflection_analyst", ticker, "Analyzing decision quality")

            # Get the decision and related signals for this ticker
            decision = portfolio_decisions[ticker]
            ticker_signals = {}

            # Collect signals from all agents for this ticker
            for agent_name, signals in analyst_signals.items():
                if agent_name != "risk_management_agent" and ticker in signals:
                    ticker_signals[agent_name] = signals[ticker]

            # Generate reflection analysis
            reflection = generate_reflection_analysis(
                ticker=ticker,
                decision=decision,
                analyst_signals=ticker_signals,
                model_name=state["metadata"]["model_name"],
                model_provider=state["metadata"]["model_provider"],
            )

            reflection_analysis[ticker] = {
                "decision_quality": reflection.decision_quality,
                "correctness_score": reflection.correctness_score,
                "key_insights": reflection.key_insights,
                "recommendations": reflection.recommendations,
                "reasoning": reflection.reasoning,
            }

            progress.update_status("reflection_analyst", ticker, "Done")

    # Save reflection analysis to file (for next day reference)
    save_reflection_to_file(reflection_analysis, end_date, tickers)

    # Create message
    message = HumanMessage(
        content=json.dumps(reflection_analysis),
        name="reflection_analyst",
    )

    # Show reasoning process
    if state["metadata"]["show_reasoning"]:
        show_agent_reasoning(reflection_analysis, "Reflection Analyst")

    # Add reflection analysis to state
    state["data"]["analyst_signals"]["reflection_analyst"] = reflection_analysis

    progress.update_status("reflection_analyst", None, "Done")

    return {
        "messages": state["messages"] + [message],
        "data": data,
    }


def generate_reflection_analysis(
    ticker: str,
    decision: dict,
    analyst_signals: dict,
    model_name: str,
    model_provider: str,
) -> ReflectionAnalysis:
    """Generate reflection analysis using LLM with intelligent data chunking and fallback mechanism"""

    # 增强输入数据验证和日志记录
    try:
        # 验证输入数据
        if not decision:
            progress.update_status("reflection_analyst", ticker, "Warning: Empty decision data")

        if not analyst_signals:
            progress.update_status("reflection_analyst", ticker, "Warning: Empty analyst signals")

        # 记录输入数据大小用于调试
        decision_str = json.dumps(decision, indent=2, ensure_ascii=False)
        signals_str = json.dumps(analyst_signals, indent=2, ensure_ascii=False)
        total_size = len(decision_str) + len(signals_str)

        progress.update_status("reflection_analyst", ticker,
                             f"Processing decision ({len(decision_str)} chars) and signals ({len(signals_str)} chars), total: {total_size} chars")

        # 如果数据量过大（超过1.5MB），使用数据分块策略
        if total_size > 1500000:  # 1.5MB threshold
            progress.update_status("reflection_analyst", ticker,
                                 f"Large payload detected ({total_size} chars), using chunking strategy")
            return generate_chunked_reflection_analysis(ticker, decision, analyst_signals, model_name, model_provider)

    except Exception as validation_error:
        progress.update_status("reflection_analyst", ticker, f"Input validation error: {validation_error}")

    # 定义模型回退策略，增加grok-3-reasoner作为优先选择
    fallback_models = []
    if model_provider.lower() == "qingyun":
        # QingYun API的回退模型列表，优先使用更强大的模型处理大数据量
        fallback_models = [
            (model_name, model_provider),  # 首先尝试原始模型
            ("grok-3-reasoner", "QingYun"),  # 新增grok-3-reasoner作为优先回退
            ("gpt-4o", "QingYun"),
            ("claude-3-5-sonnet-20241022", "QingYun"),
            ("gemini-2.0-flash-exp", "QingYun"),
            ("meta-llama/llama-4-scout", "QingYun")
        ]
    else:
        # 其他提供商保持原有逻辑
        fallback_models = [(model_name, model_provider)]

    template = ChatPromptTemplate.from_messages([
        (
            "system",
            """You are a professional investment reflection analyst responsible for analyzing the decision quality of portfolio managers.

Your tasks are:
1. Analyze whether investment decisions are reasonable and fully consider signals from all analysts
2. Evaluate the logical consistency and risk management of decisions
3. Identify strengths and potential issues in decisions
4. Provide specific improvement recommendations

Evaluation criteria:
- excellent: Decision is completely reasonable, fully considers all signals, and has proper risk control
- good: Decision is basically reasonable, considers most signals, with slight room for improvement
- fair: Decision has some reasonableness but has obvious deficiencies or insufficient signal utilization
- poor: Decision has major problems, improper signal analysis, or insufficient risk control

Please provide detailed analysis and specific improvement recommendations.""",
        ),
        (
            "human",
            """Please analyze the quality of the following investment decision:

Ticker: {ticker}

Portfolio Manager's Decision:
{decision}

Analyst Signals:
{analyst_signals}

Please return analysis in the following JSON format:
{{
  "decision_quality": "excellent" | "good" | "fair" | "poor",
  "correctness_score": score between 0-100,
  "key_insights": ["key insight 1", "key insight 2", ...],
  "recommendations": ["recommendation 1", "recommendation 2", ...],
  "reasoning": "detailed analysis reasoning process"
}}""",
        ),
    ])

    try:
        prompt = template.invoke({
            "ticker": ticker,
            "decision": json.dumps(decision, indent=2, ensure_ascii=False),
            "analyst_signals": json.dumps(analyst_signals, indent=2, ensure_ascii=False),
        })
    except Exception as prompt_error:
        progress.update_status("reflection_analyst", ticker, f"Prompt creation error: {prompt_error}")
        return create_default_reflection_with_error(f"Prompt creation failed: {prompt_error}")

    def create_default_reflection():
        return ReflectionAnalysis(
            decision_quality="fair",
            correctness_score=50.0,
            key_insights=["Error occurred during analysis process"],
            recommendations=["Need to re-evaluate decision logic"],
            reasoning="Error occurred during reflection analysis process, defaulting to fair evaluation"
        )

    # 尝试使用回退模型
    last_error = None
    for attempt_model, attempt_provider in fallback_models:
        try:
            progress.update_status("reflection_analyst", ticker,
                                 f"Attempting analysis with {attempt_model} ({attempt_provider})")

            result = call_llm(
                prompt=prompt,
                model_name=attempt_model,
                model_provider=attempt_provider,
                pydantic_model=ReflectionAnalysis,
                agent_name="reflection_analyst",
                default_factory=create_default_reflection,
            )

            # 检查是否是默认错误响应
            if result.reasoning != "Error occurred during reflection analysis process, defaulting to fair evaluation":
                progress.update_status("reflection_analyst", ticker,
                                     f"Successfully completed analysis with {attempt_model}")
                return result
            else:
                progress.update_status("reflection_analyst", ticker,
                                     f"Got default response from {attempt_model}, trying next model")
                last_error = f"Default response from {attempt_model}"

        except Exception as e:
            progress.update_status("reflection_analyst", ticker,
                                 f"Failed with {attempt_model}: {str(e)}")
            last_error = str(e)
            continue

    # 如果所有模型都失败了，返回带有详细错误信息的默认响应
    progress.update_status("reflection_analyst", ticker,
                         f"All fallback models failed. Last error: {last_error}")

    return ReflectionAnalysis(
        decision_quality="fair",
        correctness_score=50.0,
        key_insights=[f"All models failed during analysis process. Last error: {last_error}"],
        recommendations=["Check model availability", "Verify input data format", "Consider using alternative models"],
        reasoning=f"All fallback models failed during reflection analysis. Last error: {last_error}. Defaulting to fair evaluation."
    )





def save_reflection_to_file(reflection_analysis: dict, end_date: str, tickers: list[str]):
    """Save reflection analysis to file for next day use"""
    try:
        # Create reflection log directory
        reflection_dir = "reasoning_logs/reflections"
        os.makedirs(reflection_dir, exist_ok=True)

        # Use date as filename
        filename = f"{reflection_dir}/reflection_{end_date}.json"

        # Save data
        reflection_data = {
            "date": end_date,
            "tickers": tickers,
            "reflections": reflection_analysis,
            "timestamp": datetime.now().isoformat()
        }

        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(reflection_data, f, indent=2, ensure_ascii=False)

        progress.update_status("reflection_analyst", None, f"Reflection saved to {filename}")

    except Exception as e:
        progress.update_status("reflection_analyst", None, f"Failed to save reflection: {str(e)}")


def load_previous_reflection(current_date: str, tickers: list[str]) -> dict:
    """Load previous day's reflection analysis"""
    try:
        # Calculate previous trading day (simplified handling, should consider weekends and holidays in practice)
        current_dt = datetime.strptime(current_date, "%Y-%m-%d")
        previous_dt = current_dt - timedelta(days=1)
        previous_date = previous_dt.strftime("%Y-%m-%d")

        reflection_dir = "reasoning_logs/reflections"
        filename = f"{reflection_dir}/reflection_{previous_date}.json"

        if os.path.exists(filename):
            with open(filename, 'r', encoding='utf-8') as f:
                reflection_data = json.load(f)
                return reflection_data.get("reflections", {})

        return {}

    except Exception as e:
        progress.update_status("reflection_analyst", None, f"Failed to load previous reflection: {str(e)}")
        return {}


def generate_chunked_reflection_analysis(
    ticker: str,
    decision: dict,
    analyst_signals: dict,
    model_name: str,
    model_provider: str,
) -> ReflectionAnalysis:
    """
    处理大数据量的分块反思分析策略
    当分析师信号数据过大时，将其分组处理，然后合并结果
    """
    progress.update_status("reflection_analyst", ticker, "Starting chunked analysis for large dataset")

    try:
        # 将分析师信号按类型分组，减少单次请求的数据量
        signal_groups = {
            "fundamental_analysts": {},  # 基本面分析师
            "technical_analysts": {},   # 技术分析师
            "news_analysts": {},        # 新闻分析师
            "personality_analysts": {}, # 投资大师分析师
            "other_analysts": {}        # 其他分析师
        }

        # 分类分析师信号
        for agent_name, signal in analyst_signals.items():
            if any(keyword in agent_name.lower() for keyword in ['fundamental', 'valuation']):
                signal_groups["fundamental_analysts"][agent_name] = signal
            elif any(keyword in agent_name.lower() for keyword in ['technical', 'market']):
                signal_groups["technical_analysts"][agent_name] = signal
            elif any(keyword in agent_name.lower() for keyword in ['news', 'sentiment', 'social']):
                signal_groups["news_analysts"][agent_name] = signal
            elif any(keyword in agent_name.lower() for keyword in ['buffett', 'graham', 'lynch', 'munger', 'burry', 'ackman', 'wood', 'fisher', 'damodaran', 'druckenmiller']):
                signal_groups["personality_analysts"][agent_name] = signal
            else:
                signal_groups["other_analysts"][agent_name] = signal

        # 为每个组生成子分析
        group_analyses = {}
        for group_name, group_signals in signal_groups.items():
            if group_signals:  # 只处理非空组
                progress.update_status("reflection_analyst", ticker, f"Analyzing {group_name} group ({len(group_signals)} analysts)")

                group_analysis = generate_group_reflection_analysis(
                    ticker=ticker,
                    decision=decision,
                    analyst_signals=group_signals,
                    group_name=group_name,
                    model_name=model_name,
                    model_provider=model_provider
                )

                if group_analysis:
                    group_analyses[group_name] = group_analysis

        # 合并所有组的分析结果
        if group_analyses:
            progress.update_status("reflection_analyst", ticker, f"Merging analyses from {len(group_analyses)} groups")
            return merge_group_analyses(ticker, decision, group_analyses, model_name, model_provider)
        else:
            progress.update_status("reflection_analyst", ticker, "No valid group analyses generated, using fallback")
            return create_default_reflection_with_error("No valid group analyses could be generated")

    except Exception as e:
        progress.update_status("reflection_analyst", ticker, f"Chunked analysis failed: {str(e)}")
        return create_default_reflection_with_error(f"Chunked analysis error: {str(e)}")


def generate_group_reflection_analysis(
    ticker: str,
    decision: dict,
    analyst_signals: dict,
    group_name: str,
    model_name: str,
    model_provider: str,
) -> dict:
    """为特定分析师组生成反思分析"""

    # 定义模型回退策略
    fallback_models = []
    if model_provider.lower() == "qingyun":
        fallback_models = [
            (model_name, model_provider),
            ("grok-3-reasoner", "QingYun"),
            ("gpt-4o", "QingYun"),
            ("claude-3-5-sonnet-20241022", "QingYun"),
        ]
    else:
        fallback_models = [(model_name, model_provider)]

    template = ChatPromptTemplate.from_messages([
        (
            "system",
            f"""You are analyzing the {group_name} perspective on an investment decision.

Focus specifically on insights from this analyst group and provide:
1. Key insights from this group's analysis
2. How well the decision aligns with this group's signals
3. Specific recommendations from this group's perspective
4. Quality assessment of decision considering this group's input

Be concise but thorough in your analysis.""",
        ),
        (
            "human",
            """Analyze this investment decision from the {group_name} perspective:

Ticker: {ticker}
Decision: {decision}
{group_name} Signals: {analyst_signals}

Return analysis in JSON format:
{{
  "group_insights": ["insight 1", "insight 2", ...],
  "alignment_score": score 0-100,
  "group_recommendations": ["rec 1", "rec 2", ...],
  "group_reasoning": "detailed reasoning"
}}""",
        ),
    ])

    for attempt_model, attempt_provider in fallback_models:
        try:
            prompt = template.invoke({
                "ticker": ticker,
                "group_name": group_name,
                "decision": json.dumps(decision, indent=2, ensure_ascii=False),
                "analyst_signals": json.dumps(analyst_signals, indent=2, ensure_ascii=False),
            })

            result = call_llm(
                prompt=prompt,
                model_name=attempt_model,
                model_provider=attempt_provider,
                agent_name="reflection_analyst",
                default_factory=lambda: {"error": "Failed to generate group analysis"}
            )

            # 尝试解析JSON结果
            if hasattr(result, 'content'):
                try:
                    parsed_result = json.loads(result.content)
                    if "group_insights" in parsed_result:
                        return parsed_result
                except json.JSONDecodeError:
                    pass

        except Exception as e:
            progress.update_status("reflection_analyst", ticker, f"Group analysis failed with {attempt_model}: {str(e)}")
            continue

    return {}


def merge_group_analyses(
    ticker: str,
    decision: dict,
    group_analyses: dict,
    model_name: str,
    model_provider: str,
) -> ReflectionAnalysis:
    """合并多个组分析结果为最终反思分析"""

    # 定义模型回退策略
    fallback_models = []
    if model_provider.lower() == "qingyun":
        fallback_models = [
            ("grok-3-reasoner", "QingYun"),  # 使用更强大的模型进行合并
            ("gpt-4o", "QingYun"),
            ("claude-3-5-sonnet-20241022", "QingYun"),
        ]
    else:
        fallback_models = [(model_name, model_provider)]

    template = ChatPromptTemplate.from_messages([
        (
            "system",
            """You are synthesizing multiple analyst group perspectives into a comprehensive investment decision reflection.

Your task is to:
1. Integrate insights from all analyst groups
2. Provide an overall decision quality assessment
3. Give a comprehensive correctness score (0-100)
4. Synthesize key insights across all groups
5. Provide actionable recommendations

Consider the different perspectives and create a balanced, comprehensive analysis.""",
        ),
        (
            "human",
            """Synthesize the following group analyses into a comprehensive reflection:

Ticker: {ticker}
Decision: {decision}
Group Analyses: {group_analyses}

Return final analysis in JSON format:
{{
  "decision_quality": "excellent" | "good" | "fair" | "poor",
  "correctness_score": score 0-100,
  "key_insights": ["comprehensive insight 1", "insight 2", ...],
  "recommendations": ["actionable rec 1", "rec 2", ...],
  "reasoning": "comprehensive reasoning integrating all group perspectives"
}}""",
        ),
    ])

    def create_default_reflection():
        return ReflectionAnalysis(
            decision_quality="fair",
            correctness_score=50.0,
            key_insights=["Chunked analysis completed with limited integration"],
            recommendations=["Review decision with full analyst context"],
            reasoning="Chunked analysis was performed due to large data volume, but full integration was limited"
        )

    for attempt_model, attempt_provider in fallback_models:
        try:
            prompt = template.invoke({
                "ticker": ticker,
                "decision": json.dumps(decision, indent=2, ensure_ascii=False),
                "group_analyses": json.dumps(group_analyses, indent=2, ensure_ascii=False),
            })

            result = call_llm(
                prompt=prompt,
                model_name=attempt_model,
                model_provider=attempt_provider,
                pydantic_model=ReflectionAnalysis,
                agent_name="reflection_analyst",
                default_factory=create_default_reflection,
            )

            if result.reasoning != "Error occurred during reflection analysis process, defaulting to fair evaluation":
                progress.update_status("reflection_analyst", ticker, f"Successfully merged analyses with {attempt_model}")
                return result

        except Exception as e:
            progress.update_status("reflection_analyst", ticker, f"Merge failed with {attempt_model}: {str(e)}")
            continue

    return create_default_reflection()


def create_default_reflection_with_error(error_msg: str) -> ReflectionAnalysis:
    """创建带有错误信息的默认反思分析"""
    return ReflectionAnalysis(
        decision_quality="fair",
        correctness_score=50.0,
        key_insights=[f"Analysis error: {error_msg}"],
        recommendations=["Review system configuration and data processing"],
        reasoning=f"Error occurred during reflection analysis: {error_msg}"
    )
